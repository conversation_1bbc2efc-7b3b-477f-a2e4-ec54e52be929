javascript:(function(){let%20overlayMode%20%3D%20%22over%22%3B%0Aconst%20OVERLAY_MODES%20%3D%20%5B%22aus%22%2C%20%22over%22%2C%20%22difference%22%2C%20%22out%22%5D%3B%0A%0Afetch%20%3D%20new%20Proxy(fetch%2C%20%7B%20apply%3A%20(target%2C%20thisArg%2C%20argList)%20%3D%3E%20%7B%0A%09console.log(target%2C%20thisArg%2C%20argList)%3B%0A%0A%09if(!argList%5B0%5D)%20%7B%0A%09%09throw%20new%20Error(%22No%20URL%20provided%20to%20fetch%22)%3B%0A%09%7D%0A%0A%09const%20urlString%20%3D%20typeof%20argList%5B0%5D%20%3D%3D%3D%20%22object%22%20%3F%20argList%5B0%5D.url%20%3A%20argList%5B0%5D%3B%0A%0A%09let%20url%3B%0A%09try%20%7B%0A%09%09url%20%3D%20new%20URL(urlString)%3B%0A%09%7D%20catch%20(e)%20%7B%0A%09%09throw%20new%20Error(%22Invalid%20URL%20provided%20to%20fetch%22)%3B%0A%09%7D%0A%0A%09if%20(url.hostname%20%3D%3D%3D%20%22backend.wplace.live%22%20%26%26%20url.pathname.startsWith(%22%2Ffiles%2F%22))%20%7B%0A%09%09console.log(%22Intercepted%20fetch%20request%20to%20wplace.live%22)%3B%0A%09%09if(overlayMode%20!%3D%3D%20%22aus%22)%20%7B%0A%09%09%09url.host%20%3D%20%22cfp.is-a.dev%22%3B%0A%09%09%09url.pathname%20%3D%20%60%2Fwplace%24%7Burl.pathname%7D%60%3B%0A%09%09%09url.searchParams.set(%22blending%22%2C%20overlayMode)%3B%0A%09%09%09console.log(%22Modified%20URL%3A%22%2C%20url)%3B%0A%09%09%09if(typeof%20argList%5B0%5D%20%3D%3D%3D%20%22object%22)%20%7B%0A%09%09%09%09argList%5B0%5D%20%3D%20new%20Request(url%2C%20argList%5B0%5D)%3B%0A%09%09%09%7D%20else%20%7B%0A%09%09%09%09argList%5B0%5D%20%3D%20url.toString()%3B%0A%09%09%09%7D%0A%09%09%7D%0A%09%09reloadText.style.display%20%3D%20%22none%22%3B%0A%09%7D%0A%09%0A%09return%20target.apply(thisArg%2C%20argList)%3B%0A%7D%20%7D)%3B%0A%0Alet%20reloadText%20%3D%20document.createElement(%22span%22)%3B%0A%0Afunction%20patchUI()%20%7B%0A%09if(document.getElementById(%22overlay-blend-button%22))%20%7B%0A%09%09return%3B%20%2F%2F%20Button%20already%20exists%2C%20no%20need%20to%20patch%20again%0A%09%7D%0A%09let%20blendButton%20%3D%20document.createElement(%22button%22)%3B%0A%09blendButton.id%20%3D%20%22overlay-blend-button%22%3B%0A%09blendButton.textContent%20%3D%20%22Overlay%3A%20%22%20%2B%20overlayMode.charAt(0).toUpperCase()%20%2B%20overlayMode.slice(1)%3B%0A%09blendButton.style.backgroundColor%20%3D%20%22%230e0e0e7f%22%3B%0A%09blendButton.style.color%20%3D%20%22white%22%3B%0A%09blendButton.style.border%20%3D%20%22solid%22%3B%0A%09blendButton.style.borderColor%20%3D%20%22%231d1d1d7f%22%3B%0A%09blendButton.style.borderRadius%20%3D%20%224px%22%3B%0A%09blendButton.style.padding%20%3D%20%225px%2010px%22%3B%0A%09blendButton.style.cursor%20%3D%20%22pointer%22%3B%0A%09blendButton.style.backdropFilter%20%3D%20%22blur(2px)%22%3B%0A%09%0A%09blendButton.addEventListener(%22click%22%2C%20()%20%3D%3E%20%7B%0A%09%09overlayMode%20%3D%20OVERLAY_MODES%5B(OVERLAY_MODES.indexOf(overlayMode)%20%2B%201)%20%25%20OVERLAY_MODES.length%5D%3B%0A%09%09blendButton.textContent%20%3D%20%60Overlay%3A%20%24%7BoverlayMode.charAt(0).toUpperCase()%20%2B%20overlayMode.slice(1)%7D%60%3B%0A%09%09console.log(%22Overlay%20mode%20set%20to%3A%22%2C%20overlayMode)%3B%0A%09%09reloadText.style.display%20%3D%20%22%22%3B%0A%09%7D)%3B%0A%09%0A%09reloadText.textContent%20%3D%20%22Rein%20und%20wieder%20raus%20zoomen%2C%20um%20das%20Overlay%20zu%20sehen!%22%3B%0A%09reloadText.style.color%20%3D%20%22red%22%3B%0A%09reloadText.style.fontWeight%20%3D%20%22bold%22%3B%0A%09reloadText.style.maxWidth%20%3D%20%22200px%22%3B%0A%09reloadText.style.textAlign%20%3D%20%22right%22%3B%0A%09reloadText.style.backgroundColor%20%3D%20%22%23ffffff7f%22%3B%0A%09reloadText.style.borderRadius%20%3D%20%224px%22%3B%0A%09reloadText.style.backdropFilter%20%3D%20%22blur(2px)%22%3B%0A%09%0A%09const%20buttonContainer%20%3D%20document.querySelector(%22div.gap-4%3Anth-child(1)%20%3E%20div%3Anth-child(2)%22)%3B%0A%09const%20leftSidebar%20%3D%20document.querySelector(%22html%20body%20div%20div.disable-pinch-zoom.relative.h-full.overflow-hidden.svelte-6wmtgk%20div.absolute.right-2.top-2.z-30%20div.flex.flex-col.gap-4.items-center%22)%3B%0A%09%0A%09if(buttonContainer)%20%7B%0A%09%09buttonContainer.appendChild(blendButton)%3B%0A%09%09buttonContainer.appendChild(reloadText)%3B%0A%09%09buttonContainer.classList.remove(%22items-center%22)%3B%0A%09%09buttonContainer.classList.add(%22items-end%22)%3B%0A%09%7D%0A%09if(leftSidebar)%20%7B%0A%09%09leftSidebar.classList.add(%22items-end%22)%3B%0A%09%09leftSidebar.classList.remove(%22items-center%22)%3B%0A%09%7D%0A%7D%0A%0Aconst%20observer%20%3D%20new%20MutationObserver(()%20%3D%3E%20%7B%0A%09patchUI()%3B%0A%7D)%3B%0A%0Aobserver.observe(document.querySelector(%22div.gap-4%3Anth-child(1)%22)%2C%20%7B%0A%09childList%3A%20true%2C%0A%09subtree%3A%20true%0A%7D)%3B%0A%0ApatchUI()%3B%0A})();
