{"name": "wplace-proxy", "private": true, "devDependencies": {"@types/bun": "latest", "@types/express": "^5.0.3", "@types/express-fileupload": "^1.5.1", "@types/pngjs": "^6.0.5"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"express": "^5.1.0", "express-fileupload": "^1.5.2", "express-jwt": "^8.5.1", "express-rate-limit": "^8.0.1", "express-slow-down": "^2.1.0", "jsonwebtoken": "^9.0.2", "pngjs": "^7.0.0", "redis": "^5.6.1", "sharp": "^0.34.3", "uuid": "^11.1.0"}}