<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Wplace Overlay</title>
	<link rel="stylesheet" href="dracula.css">
	<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/highlight.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/javascript.min.js"></script>
	<script>
		hljs.highlightAll();
	</script>

	<style>
		body {
			box-sizing: border-box;
			margin: 10px;
			padding: 0;
			font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
			background-color: #0e0e0e;
			color: white;
		}

		h1 {
			margin: 0;
		}

		code {
			border-radius: 10px;
		}

		a {
			color: #61dafb;
			text-decoration: underline;
		}
	</style>
</head>
<body>
	<h1>Wplace Overlay v1.0-beta</h1>

	<a href="admin/">Portal for Alliance Admins</a> &middot; <a href="https://discord.gg/6anTm35dvB">Join our discord!</a>

	<br><br>

	<span>Overlay for Wplace.</span>

	<h2>Aktivieren mit Lesezeichen (HANDY):</h2>
	<a href="bookmark.txt">Lesezeichen</a>
	<span>iOS ungetestet!</span><br>
	<video src="mobile.mp4" controls></video>

	<h2>Aktivieren mit Console (NUR PC):</h2>
	<ul>
		<li>Strg+Shift+I drücken</li>
		<li>Auf den Tab "Console" wechseln</li>
		<li>Den folgenden Code in die Konsole einfügen und Enter drücken: (wenn das kopieren blockiert wird "allow pasting" eingeben und erneut versuchen)</li>
<pre><code class="language-javascript">let overlayMode = "over";
const OVERLAY_MODES = ["aus", "over", "difference", "out"];
let darken = false;

const HOST = "cfp.is-a.dev";
const SUBPATH = "/wplace";

fetch = new Proxy(fetch, { apply: (target, thisArg, argList) => {
	console.log(target, thisArg, argList);

	if(!argList[0]) {
		throw new Error("No URL provided to fetch");
	}

	const urlString = typeof argList[0] === "object" ? argList[0].url : argList[0];

	let url;
	try {
		url = new URL(urlString);
	} catch (e) {
		throw new Error("Invalid URL provided to fetch");
	}

	if (url.hostname === "backend.wplace.live" && url.pathname.startsWith("/files/")) {
		console.log("Intercepted fetch request to wplace.live");
		if(overlayMode !== "aus") {
			url.host = HOST;
			url.pathname = `${SUBPATH}${url.pathname}`;
			url.searchParams.set("blending", overlayMode);
			url.searchParams.set("darken", darken + "");
			console.log("Modified URL:", url);
			if(typeof argList[0] === "object") {
				argList[0] = new Request(url, argList[0]);
			} else {
				argList[0] = url.toString();
			}
		}
		reloadText.style.display = "none";
	}
	
	return target.apply(thisArg, argList);
} });

let reloadText = document.createElement("span");

function patchUI() {
	if(document.getElementById("overlay-blend-button")) {
		return; // Button already exists, no need to patch again
	}
	let blendButton = document.createElement("button");
	blendButton.id = "overlay-blend-button";
	blendButton.textContent = "Overlay: " + overlayMode.charAt(0).toUpperCase() + overlayMode.slice(1);
	blendButton.style.backgroundColor = "#0e0e0e7f";
	blendButton.style.color = "white";
	blendButton.style.border = "solid";
	blendButton.style.borderColor = "#1d1d1d7f";
	blendButton.style.borderRadius = "4px";
	blendButton.style.padding = "5px 10px";
	blendButton.style.cursor = "pointer";
	blendButton.style.backdropFilter = "blur(2px)";
	
	blendButton.addEventListener("click", () => {
		overlayMode = OVERLAY_MODES[(OVERLAY_MODES.indexOf(overlayMode) + 1) % OVERLAY_MODES.length];
		blendButton.textContent = `Overlay: ${overlayMode.charAt(0).toUpperCase() + overlayMode.slice(1)}`;
		console.log("Overlay mode set to:", overlayMode);
		reloadText.style.display = "";
	});

	let darkenMode = document.createElement("button");
	darkenMode.textContent = "Darken: " + (darken ? "An" : "Aus");
	darkenMode.style.backgroundColor = "#0e0e0e7f";
	darkenMode.style.color = "white";
	darkenMode.style.border = "solid";
	darkenMode.style.borderColor = "#1d1d1d7f";
	darkenMode.style.borderRadius = "4px";
	darkenMode.style.padding = "5px 10px";
	darkenMode.style.cursor = "pointer";
	darkenMode.style.backdropFilter = "blur(2px)";
	
	darkenMode.addEventListener("click", () => {
		darken = !darken;
		darkenMode.textContent = `Darken: ${darken ? "An" : "Aus"}`;
		console.log("Darken mode set to:", darken);
		reloadText.style.display = "";
	});
	
	reloadText.textContent = "Rein und wieder raus zoomen, um das Overlay zu sehen!";
	reloadText.style.color = "red";
	reloadText.style.fontWeight = "bold";
	reloadText.style.maxWidth = "200px";
	reloadText.style.textAlign = "right";
	reloadText.style.backgroundColor = "#ffffff7f";
	reloadText.style.borderRadius = "4px";
	reloadText.style.backdropFilter = "blur(2px)";
	
	const buttonContainer = document.querySelector("div.gap-4:nth-child(1) > div:nth-child(2)");
	const leftSidebar = document.querySelector("html body div div.disable-pinch-zoom.relative.h-full.overflow-hidden.svelte-6wmtgk div.absolute.right-2.top-2.z-30 div.flex.flex-col.gap-4.items-center");
	
	if(buttonContainer) {
		buttonContainer.appendChild(blendButton);
		buttonContainer.appendChild(darkenMode);
		buttonContainer.appendChild(reloadText);
		buttonContainer.classList.remove("items-center");
		buttonContainer.classList.add("items-end");
	}
	if(leftSidebar) {
		leftSidebar.classList.add("items-end");
		leftSidebar.classList.remove("items-center");
	}
}

const observer = new MutationObserver(() => {
	patchUI();
});

observer.observe(document.querySelector("div.gap-4:nth-child(1)"), {
	childList: true,
	subtree: true
});

patchUI();
</code></pre>
		<li>Raus und wieder reinzoomen um die Pixel zu aktualisieren</li>
	</ul>

	<h2>Bekannte Probleme:</h2>
	<ul>
		<li>Die Overlay Pixel werden in einer Ecke vom Pixel angezeigt, nicht in der Mitte</li>
	</ul>

	<h2>Externe Links:</h2>
	<ul>
		<li><a href="https://github.com/cfpwastaken/wplace-overlay">GitHub</a></li>
	</ul>
</body>
</html>